import React, { useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>onte<PERSON>,
  <PERSON>dalBody,
  Modal<PERSON>ooter,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Chip,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import { useNotifications } from "@/hooks/notifications/useNotifications";

interface NotificationsModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedProject: number | null;
}

export const NotificationsModal: React.FC<NotificationsModalProps> = ({
  isOpen,
  onClose,
  selectedProject,
}) => {
  const { notifications, refetch, loading } = useNotifications(
    selectedProject || 0,
  );

  useEffect(() => {
    if (!isOpen || !selectedProject) return;
    refetch();
  }, [selectedProject, isOpen]);

  return (
    <Modal isDismissable={false} isOpen={isOpen} size="3xl" onClose={onClose}>
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader>
              <h3 className="text-xl font-bold">Notificaciones</h3>
            </ModalHeader>
            <ModalBody>
              <Table removeWrapper aria-label="Tabla de notificaciones">
                <TableHeader>
                  <TableColumn>FECHA DE ENVÍO</TableColumn>
                  <TableColumn>NOTIFICACIÓN</TableColumn>
                  <TableColumn>ASUNTO</TableColumn>
                </TableHeader>
                <TableBody
                  emptyContent="No hay notificaciones"
                  isLoading={loading}
                  loadingContent={
                    <Spinner label="Cargando notificaciones..." />
                  }
                >
                  {notifications?.map((notification) => (
                    <TableRow key={notification.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Icon
                            className="text-default-400"
                            icon="lucide:clock"
                          />
                          {new Date(notification.timestamp).toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell>{notification.content}</TableCell>
                      <TableCell>
                        <Chip color="secondary" variant="flat">
                          {notification.subject}
                        </Chip>
                      </TableCell>
                    </TableRow>
                  )) || []}
                </TableBody>
              </Table>
            </ModalBody>
            <ModalFooter className="flex justify-between">
              <div>&nbsp;</div>
              <Button
                color="danger"
                radius="sm"
                variant="light"
                onPress={onClose}
              >
                Cerrar
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};
