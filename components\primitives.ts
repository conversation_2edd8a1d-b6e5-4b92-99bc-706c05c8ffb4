import { tv } from "tailwind-variants";

export const title = tv({
  base: "tracking-tight inline font-semibold",
  variants: {
    color: {
      violet: "from-[#FF1CF7] to-[#b249f8]",
      yellow: "from-[#FF705B] to-[#FFB457]",
      blue: "from-[#5EA2EF] to-[#0072F5]",
      cyan: "from-[#00b7fa] to-[#01cfea]",
      green: "from-[#6FEE8D] to-[#17c964]",
      pink: "from-[#FF72E1] to-[#F54C7A]",
      foreground: "dark:from-[#FFFFFF] dark:to-[#4B4B4B]",
    },
    size: {
      sm: "text-3xl lg:text-4xl",
      md: "text-[2.3rem] lg:text-5xl leading-9",
      lg: "text-4xl lg:text-6xl",
    },
    fullWidth: {
      true: "w-full block",
    },
  },
  defaultVariants: {
    size: "sm",
  },
  compoundVariants: [
    {
      color: [
        "violet",
        "yellow",
        "blue",
        "cyan",
        "green",
        "pink",
        "foreground",
      ],
      class: "bg-clip-text text-transparent bg-gradient-to-b",
    },
  ],
});

export const subtitle = tv({
  base: "w-full md:w-1/2 my-2 text-lg lg:text-xl text-default-600 block max-w-full",
  variants: {
    fullWidth: {
      true: "!w-full",
    },
  },
  defaultVariants: {
    fullWidth: true,
  },
});

export const getStatusStyle = (status: string, isDarkMode = false) => {
  switch (status.toLowerCase()) {
    case "en curso":
      return isDarkMode
        ? "bg-blue-900 text-blue-200 border-blue-300"
        : "bg-blue-200 text-blue-700 border-blue-700";
    case "prevista":
      return isDarkMode
        ? "bg-green-900 text-green-200 border-green-300"
        : "bg-green-200 text-green-700 border-green-700";
    case "on hold":
      return isDarkMode
        ? "bg-yellow-900 text-yellow-200 border-yellow-300"
        : "bg-yellow-200 text-yellow-700 border-yellow-700";
    case "cancelado":
      return isDarkMode
        ? "bg-red-900 text-red-200 border-red-300"
        : "bg-red-200 text-red-700 border-red-700";
    default:
      return isDarkMode
        ? "bg-gray-800 text-gray-200 border-gray-500"
        : "bg-gray-200 text-gray-700 border-gray-700";
  }
};

export const getStatusStyleText = (status: string, isDarkMode = false) => {
  switch (status.toLowerCase()) {
    case "en curso":
      return isDarkMode ? "text-green-300" : "text-green-700";
    case "prevista":
      return isDarkMode ? "text-white" : "text-black";
    case "on hold":
      return isDarkMode ? "text-white" : "text-black";
    case "cancelado":
      return isDarkMode ? "text-red-300" : "text-red-700";
    default:
      return isDarkMode ? "text-gray-300" : "text-gray-700";
  }
};

export const phaseColors = {
  start: {
    bg: "#ECF0F1",
    text: "#000000",
    border: "#68858D",
  },
  collection: {
    bg: "#F5FF79",
    text: "#000000",
    border: "#BECC00",
  },
  migration: {
    bg: "#FFC15D",
    text: "#000000",
    border: "#F59700",
  },
  test: {
    bg: "#DA8A8A",
    text: "#000000",
    border: "#C44545",
  },
  "go live": {
    bg: "#8DF082",
    text: "#000000",
    border: "#29C918",
  },
  incubadora: {
    bg: "#79D2E6",
    text: "#000000",
    border: "#2093AC",
  },
  default: {
    bg: "#00AFD7",
    text: "#000000",
    border: "#0085A3",
  },
};

export const getPhaseStyle = (phase: string, isDarkMode = false) => {
  const styles = {
    start: isDarkMode
      ? "bg-gray-600/50 text-gray-200 border-gray-400"
      : "bg-[#ECF0F1] text-[#000000] border-[#68858D]",
    collection: isDarkMode
      ? "bg-yellow-900/50 text-yellow-400 border-yellow-400"
      : "bg-[#F5FF79] text-[#000000] border-[#BECC00]",
    migration: isDarkMode
      ? "bg-orange-900/50 text-orange-400 border-orange-400"
      : "bg-[#FFC15D] text-[#000000] border-[#F59700]",
    test: isDarkMode
      ? "bg-pink-900/50 text-pink-400 border-pink-400"
      : "bg-[#DA8A8A] text-[#000000] border-[#C44545]",
    "go live": isDarkMode
      ? "bg-green-900/50 text-green-400 border-green-400"
      : "bg-[#8DF082] text-[#000000] border-[#29C918]",
    incubadora: isDarkMode
      ? "bg-blue-900/50 text-blue-400 border-blue-400"
      : "bg-[#79D2E6] text-[#000000] border-[#2093AC]",
    default: isDarkMode
      ? "bg-gray-900/50 text-gray-400 border-gray-400"
      : "bg-[#00AFD7] text-[#000000] border-[#0085A3]",
  };

  const phaseKey = phase.toLowerCase() as keyof typeof styles;

  return styles[phaseKey] || styles.default;
};

export const getPhaseStyleText = (phase: string, isDarkMode = false) => {
  const phaseKey = phase.toLowerCase() as keyof typeof phaseColors;
  const color = phaseColors[phaseKey]?.text || phaseColors.default.text;

  // Return the color as a direct string instead of trying to interpolate
  return isDarkMode ? `text-gray-200` : `text-black`;
};
