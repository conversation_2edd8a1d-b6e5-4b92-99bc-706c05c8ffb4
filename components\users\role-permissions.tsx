import React from "react";
import { Chip } from "@heroui/react";

interface RolePermissionsProps {
  permissions: string[];
}

export function RolePermissions({ permissions }: RolePermissionsProps) {
  return (
    <div className="flex flex-wrap gap-2">
      {permissions.map((permission, index) => (
        <Chip
          key={index}
          color={permission === "Administrador" ? "primary" : "default"}
          size="sm"
          variant="flat"
        >
          {permission}
        </Chip>
      ))}
    </div>
  );
}
