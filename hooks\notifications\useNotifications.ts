"use client";

import { useState } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

interface ApiNotification {
  id: number;
  notification_name: string;
  notification_description: string;
  timestamp: string;
  subject: string;
  recipients: string;
  content: string;
}

export function useNotifications(projectId: string | number) {
  const [notifications, setNotifications] = useState<ApiNotification[] | null>(
    null,
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(
        `${API_ROUTES.NOTIFICATIONS_LIST.replace("{project_id}", String(projectId))}`,
      );

      if (response.status === 200) {
        setNotifications(response.data);
      } else {
        setError("Failed to fetch notifications");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch notifications");
    } finally {
      setLoading(false);
    }
  };

  return {
    notifications,
    loading,
    error,
    refetch: fetchNotifications,
  };
}
