"use client";

import { useState } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

interface DateFields {
  id: number;
  name: string;
  is_month_year: boolean;
}

export interface DateChangeLogs {
  id: number;
  project: string;
  timestamp: string;
  phase: string;
  field_type: string;
  new_value: string;
  previous_value: string;
  user: string;
  comment: string;
}

export function useProjectDates() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [dateFields, setDateFields] = useState<DateFields[]>([]);
  const [dateChangeLogs, setDateChangeLogs] = useState<DateChangeLogs[]>([]);

  const updateProjectDates = async (
    project_id: number,
    date_field_name: string,
    new_value: string,
    phase_id: number,
    comment: string,
  ) => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.post(
        API_ROUTES.DATE_CHANGE_PROJECT,
        {
          project_id,
          date_field_name,
          new_value,
          phase_id,
          comment,
        },
      );

      if (response.status === 200) {
        return response.data;
      } else {
        setError("Failed to update project dates");
      }
    } catch (err: any) {
      setError(err.message || "Failed to update project dates");
    } finally {
      setLoading(false);
    }
  };

  const fetchDateFields = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(API_ROUTES.DATE_FIELDS);

      if (response.status === 200) {
        setDateFields(response.data);
      } else {
        setError("Failed to fetch date fields");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch date fields");
    } finally {
      setLoading(false);
    }
  };

  const fetchDateChangeLogs = async (project_id: number) => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(
        `${API_ROUTES.DATE_CHANGE_HISTORY}?project_id=${project_id}`,
      );

      if (response.status === 200) {
        setDateChangeLogs(response.data);
      } else {
        setError("Failed to fetch date change logs");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch date change logs");
    } finally {
      setLoading(false);
    }
  };

  return {
    updateProjectDates,
    fetchDateFields,
    fetchDateChangeLogs,
    dateFields,
    dateChangeLogs,

    loading,
    error,
  };
}
