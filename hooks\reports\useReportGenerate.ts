"use client";

import { useState } from "react";

// No need to import ApiReport as we're returning a blob directly
import { axiosInstance } from "@/lib/axios";
import { API_ROUTES } from "@/lib/api";

export function useReportGenerate(projectId: string | number) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateProjectPlan = async (language: string = "es") => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(
        `${API_ROUTES.GENERATE_PROJECT_PLAN.replace("{project_id}", String(projectId))}?language=${language}`,
        {
          responseType: "blob", // Important for PDF downloads
        },
      );

      if (response.status === 200) {
        return response.data; // Return the blob directly for PDF
      } else {
        throw new Error("Failed to generate project plan");
      }
    } catch (err: any) {
      const errorMsg = err.message || "Failed to generate project plan";

      setError(errorMsg);
      throw new Error(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  const generateReport = async (reportId: string | number, language: string = "es") => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(
        `${API_ROUTES.GENERATE_REPORT.replace("{report_id}", String(reportId))}?language=${language}`,
        {
          responseType: "blob", // Important for PDF downloads
        },
      );

      if (response.status === 200) {
        return response.data; // Return the blob directly for PDF
      } else {
        throw new Error("Failed to generate report");
      }
    } catch (err: any) {
      const errorMsg = err.message || "Failed to generate report";

      setError(errorMsg);
      throw new Error(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  return {
    generateProjectPlan,
    generateReport,
    isGenerating: loading,
    setIsGenerating: setLoading,
    loading,
    error,
  };
}
