"use client";

import { useState } from "react";

import { Role } from "./useUsers";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

export function useRoles() {
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchRoles = async () => {
    try {
      setLoading(true);
      setError(null);

      const responseRoles = await axiosInstance.get(API_ROUTES.ALL_ROLES);

      if (responseRoles.status === 200) {
        setRoles(responseRoles.data);
      } else {
        setError("Failed to fetch roles");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch roles");
    } finally {
      setLoading(false);
    }
  };

  const createRole = async (name: string, permissionIds: number[]) => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.post(API_ROUTES.CREATE_ROLE, {
        name,
        permissions: permissionIds,
      });

      if (response.status === 201 || response.status === 200) {
        // Refresh the roles list
        await fetchRoles();

        return { success: true, data: response.data };
      } else {
        setError("Failed to create role");

        return { success: false, error: "Failed to create role" };
      }
    } catch (err: any) {
      const errorMsg = err.message || "Failed to create role";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  const updateRole = async (roleId: number, permissionIds: number[]) => {
    try {
      setLoading(true);
      setError(null);

      const url = API_ROUTES.UPDATE_ROLE.replace("{id}", roleId.toString());
      const response = await axiosInstance.post(url, {
        permissions: permissionIds,
      });

      if (response.status === 200) {
        // Refresh the roles list
        await fetchRoles();

        return { success: true, data: response.data };
      } else {
        setError("Failed to update role");

        return { success: false, error: "Failed to update role" };
      }
    } catch (err: any) {
      const errorMsg = err.message || "Failed to update role";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  const deleteRole = async (roleId: number) => {
    try {
      setLoading(true);
      setError(null);

      const url = API_ROUTES.DELETE_ROLE.replace("{id}", roleId.toString());
      const response = await axiosInstance.delete(url);

      if (response.status === 204 || response.status === 200) {
        // Refresh the roles list
        await fetchRoles();

        return { success: true };
      } else {
        setError("Failed to delete role");

        return { success: false, error: "Failed to delete role" };
      }
    } catch (err: any) {
      const errorMsg = err.message || "Failed to delete role";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  const updateUserRole = async (user_id: number, roleId: number) => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.post(API_ROUTES.UPDATE_USER_ROLE, {
        user_id,
        role_id: roleId,
      });

      if (response.status === 200) {
        return { success: true, data: response.data };
      } else {
        setError("Failed to update user role");

        return { success: false, error: "Failed to update user role" };
      }
    } catch (err: any) {
      const errorMsg = err.message || "Failed to update user role";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  return {
    roles,
    loading,
    error,
    fetchRoles,
    createRole,
    updateRole,
    deleteRole,
    updateUserRole,
  };
}
