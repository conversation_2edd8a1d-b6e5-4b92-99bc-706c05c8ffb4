import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>pi<PERSON>ubphase } from "@/types/project-api";
import { FieldProps } from "@/types/fields";
import { Subphase } from "@/utils/dummy-data";

// Map API field types to component field types
const mapFieldType = (apiType: string): FieldProps["type"] => {
  switch (apiType.toLowerCase()) {
    case "informative":
      return "INFORMATIVE";
    case "selection":
      return "SELECTION";
    case "task":
      return "TASK";
    case "document":
      return "DOCUMENT";
    case "task_with_subtasks":
      return "TASK_WITH_SUBTASKS";
    default:
      return "INFORMATIVE";
  }
};

// Map API field status to component field value for tasks/documents
const mapFieldValue = (field: ApiField): string | undefined => {
  if (field.type === "task" || field.type === "document") {
    switch (field.status) {
      case "PENDING":
        return "pendiente";
      case "IN_PROGRESS":
        return "en_progreso";
      case "COMPLETED":
        return "completado";
      case "CANCELLED":
        return "cancelado";
      default:
        return field.value || undefined;
    }
  }

  return field.value || undefined;
};

// Transform API field to component field
export const transformApiFieldToComponent = (
  apiField: ApiField,
): FieldProps => {
  const componentField: FieldProps = {
    id: apiField.id,
    title: apiField.name,
    description: "", // API doesn't provide description, using empty string
    // type: mapFieldType(apiField.type),
    type: mapFieldType(apiField.type),
    observation: apiField.observations || "",
    milestone: apiField.is_milestone === true ? true : false,
    value: mapFieldValue(apiField),
    options: apiField.selection_options
      ? apiField.selection_options.map(
          (option: { text: string; countsAsCompleted: boolean }) => ({
            key: option.text,
            label: option.text,
            countAsCompleted: option.countsAsCompleted,
          }),
        )
      : [],
    subtasks: apiField.subtask
      ? apiField.subtask.map((subtask: any, index: number) => ({
          id: index, // Use index as ID since API doesn't provide subtask IDs
          title: subtask.title || "",
          description: subtask.description || "",
          observation: "",
          value: (() => {
            try {
              const parsedValues = JSON.parse(apiField.value || "[]");

              return Array.isArray(parsedValues) && index < parsedValues.length
                ? parsedValues[index]
                : "";
            } catch (e) {
              console.warn("Failed to parse subtask value:", apiField.value);

              return "";
            }
          })(),
        }))
      : [],
      is_active: apiField.is_active === true ? true : false,
  };

  // Add options for selection fields
  if (apiField.type === "selection" && apiField.selection_options) {
    componentField.options = apiField.selection_options.map((option: any) => ({
      key: option.key || option.value || option.id?.toString(),
      label: option.label || option.name || option.value,
      countAsCompleted: option.countAsCompleted === true ? true : false,
    }));
  }

  // Add subtasks for task_with_subtasks fields
  if (apiField.type === "task_with_subtasks" && apiField.subtask) {
    // Parse the value array if it's a JSON string
    let subtaskValues: string[] = [];

    if (apiField.value) {
      try {
        subtaskValues = JSON.parse(apiField.value);
      } catch (e) {
        console.warn("Failed to parse subtask values:", apiField.value);
        subtaskValues = [];
      }
    }

    componentField.subtasks = apiField.subtask.map(
      (subtask: any, index: number) => ({
        id: index, // Use index as ID since API doesn't provide subtask IDs
        title: subtask.title || subtask.name || "",
        description: subtask.description || "",
        observation: subtask.observations || "",
        value: subtaskValues[index] || "",
      }),
    );
  }

  return componentField;
};

// Transform API subphases to component subphases
export const transformApiSubphasesToComponent = (
  apiSubphases: ApiSubphase[],
  apiFields: ApiField[],
  phaseName: string,
): Subphase[] => {
  return apiSubphases.map((apiSubphase) => {
    // Filter fields that belong to this subphase
    const subphaseFields = apiFields
      .filter((field) => field.subphase === apiSubphase.name)
      .map(transformApiFieldToComponent);

    return {
      id: apiSubphase.id,
      order: apiSubphase.order,
      title: apiSubphase.name,
      phase: phaseName,
      fields: subphaseFields,
      percentage: apiSubphase.percentage,
    };
  });
};

// Get phase ID by phase name
export const getPhaseIdByName = (
  phases: ApiPhase[],
  phaseName: string,
): number | null => {
  const phaseNameToId: Record<string, number> = {
    START: 1,
    COLLECTION: 2,
    MIGRATION: 3,
    TEST: 4,
    "GO LIVE": 5,
    INCUBADORA: 6,
  };
  const phaseNameUpper = phaseName.toUpperCase();

  if (phaseNameToId[phaseNameUpper]) return phaseNameToId[phaseNameUpper];
  else return -1;
};

// Get phase name by phase ID
export const getPhaseNameById = (
  phases: ApiPhase[],
  phaseId: number,
): string | null => {
  // Map phase IDs to their expected names
  const phaseIdToName: Record<number, string> = {
    1: "START",
    2: "COLLECTION",
    3: "MIGRATION",
    4: "TEST",
    5: "GO LIVE",
    6: "INCUBADORA",
  };

  // First try to find by exact ID match in the API data
  const phase = phases.find((p) => p.id === phaseId);

  if (phase) {
    return mapApiPhaseToComponent(phase.name);
  }

  // Fallback to the expected name mapping
  return phaseIdToName[phaseId] || null;
};

// Map API phase names to component phase constants
export const mapApiPhaseToComponent = (apiPhaseName: string): string => {
  switch (apiPhaseName.toUpperCase()) {
    case "START":
      return "START";
    case "COLLECTION":
      return "COLLECTION";
    case "MIGRATION":
      return "MIGRATION";
    case "TEST":
      return "TEST";
    case "GO LIVE":
    case "GOLIVE":
      return "GO LIVE";
    case "INCUBADORA":
      return "INCUBADORA";
    default:
      return apiPhaseName;
  }
};

// Transform component field updates back to API format
// This function only processes the fields that have been modified (updatedFields),
// not all fields, ensuring efficient API calls with minimal data transfer
export const transformComponentFieldsToApi = (
  updatedFields: Record<number, string>,
  originalFields: ApiField[],
) => {
  const fieldsToUpdate = Object.entries(updatedFields).map(
    ([fieldId, value]) => {
      const originalField = originalFields.find(
        (f) => f.id === parseInt(fieldId),
      );

      return {
        field_id: parseInt(fieldId),
        value: value,
        observations: originalField?.observations || "",
      };
    },
  );

  return { fields: fieldsToUpdate };
};
